package services

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
	"os"
	"time"
)

type OrderService struct {
	orderRepo       ports.OrderRepository
	orderDomainRepo ports.OrderDomainRepository
	userRepo        ports.UserRepository
	namespaceRepo   ports.NamespaceRepository
}

// OrderItem represents an item in the order request
type OrderItem struct {
	ProductCode     string  `json:"productCode"`
	Quantity        int     `json:"quantity"`
	DiscountPercent float64 `json:"discountPercent"`
	UnitPrice       float64 `json:"unitPrice"`
	CreditAmount    float64 `json:"creditAmount"`
}

// OrderRequest represents the request body for creating an order
type OrderRequest struct {
	AgentCode     string      `json:"agentCode"`
	Items         []OrderItem `json:"items"`
	PaymentMethod string      `json:"paymentMethod"`
	Notes         string      `json:"notes"`
	TotalAmount   float64     `json:"totalAmount"`
	CreditAmount  float64     `json:"creditAmount"`
}

func NewOrderService(
	orderRepo ports.OrderRepository,
	orderDomainRepo ports.OrderDomainRepository,
	userRepo ports.UserRepository,
	namespaceRepo ports.NamespaceRepository,
) ports.OrderService {
	return &OrderService{
		orderRepo:       orderRepo,
		orderDomainRepo: orderDomainRepo,
		userRepo:        userRepo,
		namespaceRepo:   namespaceRepo,
	}
}

func (s *OrderService) Create(name, code, description, lineCode string, isConfirmed bool, userID, templateID, duration uint64, orderDomains []ports.CreateOrderDomainData) (*domain.Order, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if code == "" {
		return nil, errors.New("code is required")
	}
	if lineCode == "" {
		return nil, errors.New("line code is required")
	}
	if userID == 0 {
		return nil, errors.New("user ID is required")
	}
	if templateID == 0 {
		return nil, errors.New("template ID is required")
	}

	// Verify user exists
	_, err := s.userRepo.FindByID(uint(userID))
	if err != nil {
		return nil, errors.New("user not found")
	}

	// Verify template (namespace) exists
	template, err := s.namespaceRepo.FindByID(templateID)
	if err != nil {
		return nil, errors.New("template not found")
	}

	// Verify template is of type "template"
	if template.Type != domain.NamespaceTypeTemplate {
		return nil, errors.New("namespace must be of type 'template'")
	}

	// Create order
	order := &domain.Order{
		Name:        name,
		Code:        code,
		Description: description,
		LineCode:    lineCode,
		IsConfirmed: isConfirmed,
		UserID:      userID,
		TemplateID:  templateID,
		Duration:    duration,
	}

	err = s.orderRepo.Insert(order)
	if err != nil {
		return nil, err
	}

	// Create order domains if provided
	for _, domainData := range orderDomains {
		orderDomain := &domain.OrderDomain{
			Name:        domainData.Name,
			IsAvailable: false, // Always set to false on creation as per requirements
			Price:       domainData.Price,
			OrderID:     order.ID,
			Nameserver1: domainData.Nameserver1,
			Nameserver2: domainData.Nameserver2,
			IsInternal:  domainData.IsInternal,
			IsExternal:  domainData.IsExternal,
		}

		err = s.orderDomainRepo.Insert(orderDomain)
		if err != nil {
			// If order domain creation fails, we should handle this appropriately
			// For now, we'll continue but in production you might want to rollback
			continue
		}
	}

	agentEndpoint := os.Getenv("AGENT_ENDPOINT")
	apiURL := fmt.Sprintf("%s/internal/orders", agentEndpoint)
	apiKey := os.Getenv("AGENT_API_KEY")

	// Create order items - first item is always SYS_BASIC_1M
	items := []OrderItem{
		{
			ProductCode:     "SYS_BASIC_1M",
			Quantity:        int(duration),
			DiscountPercent: 0,
			UnitPrice:       35000,
			CreditAmount:    0,
		},
	}

	// Add domain items from orderDomains
	for _, domainData := range orderDomains {
		domainItem := OrderItem{
			ProductCode:     "DOMAIN",
			Quantity:        1,
			DiscountPercent: 0,
			UnitPrice:       domainData.Price,
			CreditAmount:    0,
		}
		items = append(items, domainItem)
	}

	// Calculate total amount by summing all item prices
	var totalAmount float64
	for _, item := range items {
		totalAmount += item.UnitPrice * float64(item.Quantity)
	}

	orderRequest := OrderRequest{
		AgentCode:     lineCode,
		Items:         items,
		PaymentMethod: "crypto",
		Notes:         "",
		TotalAmount:   totalAmount,
		CreditAmount:  0,
	}

	createOrderAgent, err := s.CallApi(apiURL, apiKey, orderRequest)
	if err != nil {
		return nil, err
	}

	fmt.Printf("createOrderAgent: %+v\n", createOrderAgent)

	// Reload order with all relationships including the newly created order domains
	return s.orderRepo.FindByID(order.ID)
}

func (s *OrderService) GetAll(filter *ports.OrderFilter) ([]*domain.Order, error) {
	return s.orderRepo.FindAll(filter)
}

func (s *OrderService) GetByID(id uint64) (*domain.Order, error) {
	if id == 0 {
		return nil, errors.New("id is required")
	}

	order, err := s.orderRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("order not found")
	}

	return order, nil
}

func (s *OrderService) Update(id uint64, name, code, description, lineCode string, isConfirmed bool, userID, templateID, duration uint64) (*domain.Order, error) {
	if id == 0 {
		return nil, errors.New("id is required")
	}
	if name == "" {
		return nil, errors.New("name is required")
	}
	if code == "" {
		return nil, errors.New("code is required")
	}
	if lineCode == "" {
		return nil, errors.New("line code is required")
	}
	if userID == 0 {
		return nil, errors.New("user ID is required")
	}
	if templateID == 0 {
		return nil, errors.New("template ID is required")
	}

	// Find existing order
	order, err := s.orderRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("order not found")
	}

	// Verify user exists
	_, err = s.userRepo.FindByID(uint(userID))
	if err != nil {
		return nil, errors.New("user not found")
	}

	// Verify template (namespace) exists
	template, err := s.namespaceRepo.FindByID(templateID)
	if err != nil {
		return nil, errors.New("template not found")
	}

	// Verify template is of type "template"
	if template.Type != domain.NamespaceTypeTemplate {
		return nil, errors.New("namespace must be of type 'template'")
	}

	// Update order fields
	order.Name = name
	order.Code = code
	order.Description = description
	order.LineCode = lineCode
	order.IsConfirmed = isConfirmed
	order.UserID = userID
	order.TemplateID = templateID
	order.Duration = duration

	err = s.orderRepo.Update(order)
	if err != nil {
		return nil, err
	}

	return order, nil
}

func (s *OrderService) UpdateConfirmation(id uint64, isConfirmed bool) (*domain.Order, error) {
	if id == 0 {
		return nil, errors.New("id is required")
	}

	// Find existing order
	order, err := s.orderRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("order not found")
	}

	// Update confirmation status
	order.IsConfirmed = isConfirmed

	err = s.orderRepo.Update(order)
	if err != nil {
		return nil, err
	}

	return order, nil
}

func (s *OrderService) Delete(id uint64) error {
	if id == 0 {
		return errors.New("id is required")
	}

	// Verify order exists
	_, err := s.orderRepo.FindByID(id)
	if err != nil {
		return errors.New("order not found")
	}

	return s.orderRepo.Delete(id)
}

// call create agent to AG
func (s *OrderService) CallApi(url, apiKey string, orderReq OrderRequest) (*MockAPIResponse, error) {
	client := &http.Client{
		Timeout: 300 * time.Second,
	}

	// Marshal the request body to JSON
	requestBody, err := json.Marshal(orderReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %v", err)
	}

	// Create POST request with body
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("x-api-key", apiKey)

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse JSON response
	var apiResponse MockAPIResponse
	if err := json.Unmarshal(body, &apiResponse); err != nil {
		// Try to parse as a direct data object (without wrapper)
		var directData interface{}
		if jsonErr := json.Unmarshal(body, &directData); jsonErr == nil {
			apiResponse = MockAPIResponse{
				Status:  "success",
				Message: "Order created successfully",
				Data:    directData,
			}
		} else {
			// If all JSON parsing fails, create a generic response with the raw body
			apiResponse = MockAPIResponse{
				Status:  "success",
				Message: "Request completed successfully",
				Data:    string(body),
			}
		}
	}

	return &apiResponse, nil
}
